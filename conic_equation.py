import sympy as sp
from sympy import symbols, expand

# 定义符号变量
t, x, y, z = symbols('t x y z', real=True)

def get_conic_equation():
    """
    返回3D圆锥曲线的标准方程
    原始等式: sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)
    整理后: (|MF|² - 100) * |MC|² - (MF·MC)² = 0
    """
    
    # 定义向量
    MC = [x - 20000 + 298.5185*t, y, z - 2000 + 29.776*t]
    MF = [298.5185*t - 2812, 0, 26.776*t - 248.204]
    
    # 计算模长的平方
    MC_squared = MC[0]**2 + MC[1]**2 + MC[2]**2
    MF_squared = MF[0]**2 + MF[1]**2 + MF[2]**2
    
    # 计算点积
    dot_MF_MC = MF[0]*MC[0] + MF[1]*MC[1] + MF[2]*MC[2]
    
    # 构建方程: (|MF|² - 100) * |MC|² - (MF·MC)² = 0
    equation = (MF_squared - 100) * MC_squared - dot_MF_MC**2
    
    # 展开方程
    equation_expanded = expand(equation)
    
    return equation_expanded, MF_squared, MC_squared, dot_MF_MC

if __name__ == "__main__":
    print("=== 3D圆锥曲线标准方程 ===\n")
    
    equation, MF_sq, MC_sq, dot_prod = get_conic_equation()
    
    print("标准形式:")
    print("F(x,y,z,t) = 0")
    print("\n其中:")
    print("F(x,y,z,t) =", equation)
    
    print("\n\n=== 组成部分 ===")
    print("MF² =", expand(MF_sq))
    print("MC² =", expand(MC_sq))
    print("MF·MC =", expand(dot_prod))
    
    print("\n=== 约束条件 ===")
    print("MF² ≥ 100")
    
    print("\n=== 使用方法 ===")
    print("1. 代入具体的 (x,y,z) 坐标")
    print("2. 求解关于 t 的方程 F(x,y,z,t) = 0")
    print("3. 得到的 t 值对应圆锥曲线上的点")
