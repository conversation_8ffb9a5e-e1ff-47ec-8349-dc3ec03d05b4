import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import brentq

def equation1(t):
    """方程1: 点 (0, 193, 0)"""
    abs_MF = np.sqrt((26.776*t - 248.204)**2 + (298.5185*t - 2812)**2)
    
    if abs_MF < 10:
        return float('inf')
    
    left = np.sqrt(1 - 100/abs_MF)
    numerator = (26.776*t - 248.204)*(29.776*t - 2000) + (298.5185*t - 20000)*(298.5185*t - 2812)
    denominator = abs_MF * np.sqrt((29.776*t - 2000)**2 + (298.5185*t - 20000)**2 + 37249)
    right = numerator / denominator
    
    return left - right

def equation2(t):
    """方程2: 点 (0, 207, 10)"""
    abs_MF = np.sqrt((26.776*t - 248.204)**2 + (298.5185*t - 2812)**2)
    
    if abs_MF < 10:
        return float('inf')
    
    left = np.sqrt(1 - 100/abs_MF)
    numerator = (26.776*t - 248.204)*(29.776*t - 1990) + (298.5185*t - 20000)*(298.5185*t - 2812)
    denominator = abs_MF * np.sqrt((29.776*t - 1990)**2 + (298.5185*t - 20000)**2 + 42849)
    right = numerator / denominator
    
    return left - right

def comprehensive_search(equation_func, equation_name, plot=False):
    """全面搜索所有可能的解"""
    print(f"=== {equation_name} 全面搜索 ===")
    
    # 约束条件边界
    t_boundary1 = 9.388082
    t_boundary2 = 9.449224
    
    # 定义多个搜索区间
    search_intervals = [
        # 约束条件右侧的多个区间
        (t_boundary2, t_boundary2 + 10),
        (t_boundary2 + 10, t_boundary2 + 30),
        (t_boundary2 + 30, t_boundary2 + 70),
        (t_boundary2 + 70, t_boundary2 + 100),
        (t_boundary2 + 100, t_boundary2 + 200),
        (t_boundary2 + 200, t_boundary2 + 500),
        
        # 约束条件左侧的多个区间
        (t_boundary1 - 10, t_boundary1),
        (t_boundary1 - 30, t_boundary1 - 10),
        (t_boundary1 - 70, t_boundary1 - 30),
        (t_boundary1 - 100, t_boundary1 - 70),
        (t_boundary1 - 200, t_boundary1 - 100),
        (t_boundary1 - 500, t_boundary1 - 200),
        
        # 远距离区间
        (t_boundary2 + 500, t_boundary2 + 1000),
        (t_boundary1 - 1000, t_boundary1 - 500),
    ]
    
    all_solutions = []
    
    for i, (start, end) in enumerate(search_intervals):
        print(f"搜索区间 {i+1}: [{start:.3f}, {end:.3f}]")
        
        # 高密度采样
        t_samples = np.linspace(start, end, 5000)
        f_values = []
        
        for t in t_samples:
            try:
                f_val = equation_func(t)
                if not np.isinf(f_val) and not np.isnan(f_val):
                    f_values.append(f_val)
                else:
                    f_values.append(np.inf)
            except:
                f_values.append(np.inf)
        
        f_values = np.array(f_values)
        valid_indices = ~np.isinf(f_values)
        
        if np.sum(valid_indices) < 2:
            print("  无有效点")
            continue
        
        valid_t = t_samples[valid_indices]
        valid_f = f_values[valid_indices]
        
        # 统计信息
        min_f = np.min(valid_f)
        max_f = np.max(valid_f)
        print(f"  函数值范围: [{min_f:.6f}, {max_f:.6f}]")
        
        # 寻找符号变化
        solutions_in_interval = []
        for j in range(len(valid_f) - 1):
            if valid_f[j] * valid_f[j+1] < 0:  # 符号变化
                try:
                    t_root = brentq(equation_func, valid_t[j], valid_t[j+1])
                    residual = abs(equation_func(t_root))
                    if residual < 1e-12:
                        solutions_in_interval.append(t_root)
                        print(f"    解: t = {t_root:.10f} (残差: {residual:.2e})")
                except:
                    pass
        
        print(f"  找到 {len(solutions_in_interval)} 个解")
        all_solutions.extend(solutions_in_interval)
    
    # 去重
    unique_solutions = []
    for sol in all_solutions:
        is_unique = True
        for existing in unique_solutions:
            if abs(sol - existing) < 1e-8:
                is_unique = False
                break
        if is_unique:
            unique_solutions.append(sol)
    
    unique_solutions.sort()
    
    print(f"\n{equation_name} 所有解: {len(unique_solutions)} 个")
    for i, sol in enumerate(unique_solutions):
        print(f"t_{i+1} = {sol:.12f}")
        
        # 验证每个解
        residual = abs(equation_func(sol))
        abs_MF = np.sqrt((26.776*sol - 248.204)**2 + (298.5185*sol - 2812)**2)
        print(f"     残差: {residual:.2e}, |MF| = {abs_MF:.3f}")
    
    # 绘图（可选）
    if plot and unique_solutions:
        plt.figure(figsize=(15, 8))
        
        # 绘制函数在解附近的行为
        for i, sol in enumerate(unique_solutions):
            plt.subplot(2, len(unique_solutions), i+1)
            
            # 解附近的小范围
            t_local = np.linspace(sol - 5, sol + 5, 1000)
            f_local = [equation_func(t) if not np.isinf(equation_func(t)) else np.nan for t in t_local]
            
            plt.plot(t_local, f_local, 'b-', linewidth=2)
            plt.axhline(y=0, color='r', linestyle='--', alpha=0.7)
            plt.axvline(x=sol, color='g', linestyle=':', alpha=0.7)
            plt.scatter([sol], [0], color='red', s=100, zorder=5)
            plt.xlabel('t')
            plt.ylabel('F(t)')
            plt.title(f'解 {i+1}: t = {sol:.3f}')
            plt.grid(True, alpha=0.3)
        
        # 绘制整体函数行为
        plt.subplot(2, 1, 2)
        t_global = np.linspace(-100, 200, 3000)
        f_global = []
        for t in t_global:
            try:
                f_val = equation_func(t)
                if not np.isinf(f_val) and abs(f_val) < 10:  # 限制y轴范围
                    f_global.append(f_val)
                else:
                    f_global.append(np.nan)
            except:
                f_global.append(np.nan)
        
        plt.plot(t_global, f_global, 'b-', linewidth=1)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.7)
        
        # 标记所有解
        for sol in unique_solutions:
            plt.axvline(x=sol, color='g', linestyle=':', alpha=0.7)
            plt.scatter([sol], [0], color='red', s=50, zorder=5)
        
        plt.xlabel('t')
        plt.ylabel('F(t)')
        plt.title(f'{equation_name} 全局视图')
        plt.grid(True, alpha=0.3)
        plt.ylim(-2, 2)
        
        plt.tight_layout()
        plt.show()
    
    return unique_solutions

if __name__ == "__main__":
    print("=== 全面搜索所有可能的解 ===\n")
    
    # 搜索方程1的所有解
    solutions1 = comprehensive_search(equation1, "方程1 [点(0, 193, 0)]", plot=False)
    
    print("\n" + "="*80 + "\n")
    
    # 搜索方程2的所有解
    solutions2 = comprehensive_search(equation2, "方程2 [点(0, 207, 10)]", plot=False)
    
    print("\n" + "="*80)
    print("最终总结:")
    print(f"方程1 [点(0, 193, 0)] 共找到 {len(solutions1)} 个解:")
    for i, sol in enumerate(solutions1):
        print(f"  t_{i+1} = {sol:.12f}")
    
    print(f"\n方程2 [点(0, 207, 10)] 共找到 {len(solutions2)} 个解:")
    for i, sol in enumerate(solutions2):
        print(f"  t_{i+1} = {sol:.12f}")
    
    if len(solutions1) == 1 and len(solutions2) == 1:
        print(f"\n结论: 每个方程都只有唯一解")
        print(f"时间差: {abs(solutions2[0] - solutions1[0]):.6f} 秒")
    else:
        print(f"\n发现了多个解！需要进一步分析其物理意义。")
