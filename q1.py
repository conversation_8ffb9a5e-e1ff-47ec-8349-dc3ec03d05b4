import math
import numpy as np
import sympy as sp
from sympy import symbols, sqrt, solve, simplify

# 定义符号变量
t, x, y, z = symbols('t x y z', real=True)

# 定义3D向量 MC 和 MF
# MC = (x-20000+298.5185*t, y, z-2000+29.7760*t)
# MF = (298.5185*t-2812, 0, 26.7760*t-248.204)

def vector_magnitude(vector):
    """计算3D向量的模长"""
    return sqrt(sum(component**2 for component in vector))

def dot_product(v1, v2):
    """计算两个3D向量的点积"""
    return sum(a*b for a, b in zip(v1, v2))

# 定义向量
MC = (x - 20000 + 298.5185*t, y, z - 2000 + 29.7760*t)
MF = (298.5185*t - 2812, 0, 26.7760*t - 248.204)

# 计算向量模长
abs_MC = vector_magnitude(MC)
abs_MF = vector_magnitude(MF)

# 计算点积
dot_MF_MC = dot_product(MF, MC)

print("MC向量:", MC)
print("MF向量:", MF)
print("MC的模长:", abs_MC)
print("MF的模长:", abs_MF)
print("MF·MC点积:", dot_MF_MC)

# 原始等式: sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)
# 这个等式表示的是余弦定理的变形
left_side = sqrt(1 - 100/abs_MF)
right_side = dot_MF_MC / (abs_MF * abs_MC)

print("\n等式左边:", left_side)
print("等式右边:", right_side)

# 建立等式
equation = left_side - right_side

print("\n完整等式:", equation, "= 0")

# 如果你想求解特定条件下的t值，可以给x, y, z赋值后求解
# 例如，假设某个特定点的坐标
def solve_for_specific_point(x_val, y_val, z_val):
    """为特定点求解t值"""
    eq_substituted = equation.subs([(x, x_val), (y, y_val), (z, z_val)])
    solutions = solve(eq_substituted, t)
    return solutions

# 示例：求解t的表达式（这是一个复杂的方程）
print("\n尝试求解t的一般表达式...")
try:
    # 这可能会很复杂，我们先简化
    simplified_eq = simplify(equation)
    print("简化后的等式:", simplified_eq, "= 0")
except:
    print("等式过于复杂，无法直接简化")