import sympy as sp
from sympy import symbols, sqrt, expand

# 定义符号变量
t, x, y, z = symbols('t x y z', real=True)

print("=== 原始圆锥曲线方程 ===\n")

# 定义3D向量
print("定义向量:")
MC = [x - 20000 + 298.5185*t, y, z - 2000 + 29.776*t]
MF = [298.5185*t - 2812, 0, 26.776*t - 248.204]

print("MC =", MC)
print("MF =", MF)
print()

# 计算向量模长
abs_MC = sqrt(MC[0]**2 + MC[1]**2 + MC[2]**2)
abs_MF = sqrt(MF[0]**2 + MF[1]**2 + MF[2]**2)

print("向量模长:")
print("|MC| =", abs_MC)
print("|MF| =", abs_MF)
print()

# 计算点积
dot_MF_MC = MF[0]*MC[0] + MF[1]*MC[1] + MF[2]*MC[2]
print("点积 MF·MC =", dot_MF_MC)
print()

# 原始等式的左边和右边
left_side = sqrt(1 - 100/abs_MF)
right_side = dot_MF_MC / (abs_MF * abs_MC)

print("=== 原始等式 ===")
print("sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)")
print()
print("左边: sqrt(1 - 100/|MF|) =", left_side)
print("右边: (MF·MC)/(|MF|*|MC|) =", right_side)
print()

# 建立等式
equation = left_side - right_side
print("完整等式:")
print("sqrt(1 - 100/|MF|) - (MF·MC)/(|MF|*|MC|) = 0")
print()
print("即:")
print("F(x,y,z,t) =", equation, "= 0")

print("=== 使用说明 ===")
print("1. 这是原始的圆锥曲线方程")
print("2. 给定具体的 (x,y,z) 值后，可以求解对应的 t 值")
print("3. 约束条件: |MF| ≥ 10 (即 1 - 100/|MF| ≥ 0)")
print("4. 保持了最初等式的优美形式")

# 如果需要展开各个组成部分
print("\n=== 组成部分展开 ===")
print("|MC| =", abs_MC)
print("|MF| =", abs_MF)
print("MF·MC =", expand(dot_MF_MC))

# ========== 求解特定点的函数 ==========
def solve_for_point(x_val, y_val, z_val):
    """
    给定具体的 (x,y,z) 坐标，求解对应的 t 值
    """
    from sympy import solve, N

    print(f"\n=== 求解点 ({x_val}, {y_val}, {z_val}) 对应的 t 值 ===")

    # 代入具体数值
    equation_substituted = equation.subs([(x, x_val), (y, y_val), (z, z_val)])

    print("代入后的方程:")
    print("F(t) =", equation_substituted, "= 0")
    print()

    try:
        # 求解 t
        solutions = solve(equation_substituted, t)

        print(f"找到 {len(solutions)} 个解:")
        for i, sol in enumerate(solutions):
            print(f"t_{i+1} = {sol}")
            # 计算数值解
            try:
                numerical_val = N(sol, 6)  # 保留6位有效数字
                print(f"     ≈ {numerical_val}")
            except:
                print("     (无法计算数值解)")

        # 验证解的有效性（检查约束条件）
        print("\n验证约束条件 |MF| ≥ 10:")
        valid_solutions = []

        for i, sol in enumerate(solutions):
            try:
                # 计算对应的 |MF| 值
                MF_val = abs_MF.subs([(x, x_val), (y, y_val), (z, z_val), (t, sol)])
                MF_numerical = N(MF_val, 6)

                if MF_numerical >= 10:
                    print(f"t_{i+1}: |MF| = {MF_numerical} ✓ (满足约束)")
                    valid_solutions.append(sol)
                else:
                    print(f"t_{i+1}: |MF| = {MF_numerical} ✗ (不满足约束)")
            except:
                print(f"t_{i+1}: 无法验证约束条件")

        return valid_solutions

    except Exception as e:
        print("求解失败:", str(e))
        print("方程可能过于复杂，建议使用数值方法求解")
        return []

# 代入测试点获得只含t的表达式
def get_t_expression(x_val, y_val, z_val):
    """
    代入具体的 (x,y,z) 坐标，得到只含t的表达式
    """
    from sympy import simplify

    print(f"\n=== 代入测试点 ({x_val}, {y_val}, {z_val}) ===")

    # 代入具体数值
    left_substituted = left_side.subs([(x, x_val), (y, y_val), (z, z_val)])
    right_substituted = right_side.subs([(x, x_val), (y, y_val), (z, z_val)])
    equation_substituted = equation.subs([(x, x_val), (y, y_val), (z, z_val)])

    print("代入后的表达式:")
    print("左边: sqrt(1 - 100/|MF|) =", left_substituted)
    print()
    print("右边: (MF·MC)/(|MF|*|MC|) =", right_substituted)
    print()
    print("完整方程 F(t) = 0:")
    print("F(t) =", equation_substituted)
    print()

    # 尝试简化表达式
    try:
        simplified = simplify(equation_substituted)
        print("简化后的方程:")
        print("F(t) =", simplified, "= 0")
    except:
        print("表达式过于复杂，无法进一步简化")

    return equation_substituted

# 示例使用
if __name__ == "__main__":
    print("\n" + "="*60)
    print("代入测试点获得t的表达式")

    # 测试点1: (0, 207, 10)
    print("\n--- 测试点1 ---")
    expr1 = get_t_expression(0, 207, 10)

    # 测试点2: (0, 193, 0)
    print("\n--- 测试点2 ---")
    expr2 = get_t_expression(0, 193, 0)

    print("\n" + "="*60)
    print("使用说明:")
    print("1. get_t_expression(x, y, z): 获得代入点后只含t的表达式")
    print("2. 返回的表达式可以用于后续分析或数值求解")
    print("3. 表达式形式: F(t) = 0")
    print("4. 可以复制表达式到其他工具中求解")