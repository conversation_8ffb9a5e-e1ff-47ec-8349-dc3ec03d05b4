import sympy as sp
from sympy import symbols, sqrt, expand

# 定义符号变量
t, x, y, z = symbols('t x y z', real=True)

print("=== 原始圆锥曲线方程 ===\n")

# 定义3D向量
print("定义向量:")
MC = [x - 20000 + 298.5185*t, y, z - 2000 + 29.776*t]
MF = [298.5185*t - 2812, 0, 26.776*t - 248.204]

print("MC =", MC)
print("MF =", MF)
print()

# 计算向量模长
abs_MC = sqrt(MC[0]**2 + MC[1]**2 + MC[2]**2)
abs_MF = sqrt(MF[0]**2 + MF[1]**2 + MF[2]**2)

print("向量模长:")
print("|MC| =", abs_MC)
print("|MF| =", abs_MF)
print()

# 计算点积
dot_MF_MC = MF[0]*MC[0] + MF[1]*MC[1] + MF[2]*MC[2]
print("点积 MF·MC =", dot_MF_MC)
print()

# 原始等式的左边和右边
left_side = sqrt(1 - 100/abs_MF)
right_side = dot_MF_MC / (abs_MF * abs_MC)

print("=== 原始等式 ===")
print("sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)")
print()
print("左边: sqrt(1 - 100/|MF|) =", left_side)
print("右边: (MF·MC)/(|MF|*|MC|) =", right_side)
print()

# 建立等式
equation = left_side - right_side
print("完整等式:")
print("sqrt(1 - 100/|MF|) - (MF·MC)/(|MF|*|MC|) = 0")
print()
print("即:")
print("F(x,y,z,t) =", equation, "= 0")

print("=== 使用说明 ===")
print("1. 这是原始的圆锥曲线方程")
print("2. 给定具体的 (x,y,z) 值后，可以求解对应的 t 值")
print("3. 约束条件: |MF| ≥ 10 (即 1 - 100/|MF| ≥ 0)")
print("4. 保持了最初等式的优美形式")

# 如果需要展开各个组成部分
print("\n=== 组成部分展开 ===")
print("|MC| =", abs_MC)
print("|MF| =", abs_MF)
print("MF·MC =", expand(dot_MF_MC))