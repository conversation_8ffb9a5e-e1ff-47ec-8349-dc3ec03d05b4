import sympy as sp
from sympy import symbols, sqrt, expand, simplify

# 定义符号变量
t, x, y, z = symbols('t x y z', real=True)

print("=== 3D圆锥曲线方程整理 ===\n")

# 定义3D向量
print("定义向量:")
MC = [x - 20000 + 298.5185*t, y, z - 2000 + 29.776*t]
MF = [298.5185*t - 2812, 0, 26.776*t - 248.204]

print("MC =", MC)
print("MF =", MF)
print()

# 计算向量模长的平方（避免根号复杂化）
MC_squared = MC[0]**2 + MC[1]**2 + MC[2]**2
MF_squared = MF[0]**2 + MF[1]**2 + MF[2]**2

print("计算模长的平方:")
print("|MC|² =", expand(MC_squared))
print("|MF|² =", expand(MF_squared))
print()

# 计算点积
dot_MF_MC = MF[0]*MC[0] + MF[1]*MC[1] + MF[2]*MC[2]
print("点积 MF·MC =", expand(dot_MF_MC))
print()

# 原始等式: sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)
# 为了避免复杂的根号，我们两边平方
print("原始等式: sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)")
print("两边平方得到:")
print("(1 - 100/|MF|) = (MF·MC)²/(|MF|²*|MC|²)")
print()

# 用模长的平方表示
print("用模长的平方表示:")
print("(1 - 100/sqrt(|MF|²)) = (MF·MC)²/(|MF|²*|MC|²)")
print()

# 进一步整理，消除分母
print("消除分母，整理得到标准形式:")
print("(|MF|² - 100) * |MF|² * |MC|² = (MF·MC)² * |MF|²")
print("即: (|MF|² - 100) * |MC|² = (MF·MC)²")
print()

# 代入具体表达式
left_standard = (MF_squared - 100) * MC_squared
right_standard = dot_MF_MC**2

print("=== 最终的3D圆锥曲线方程 ===")
print("(|MF|² - 100) * |MC|² - (MF·MC)² = 0")
print()
print("展开后的完整方程:")
equation_expanded = expand(left_standard - right_standard)
print("F(x,y,z,t) =", equation_expanded, "= 0")
print()

print("=== 使用说明 ===")
print("1. 这是关于 (x,y,z,t) 的四元方程")
print("2. 给定具体的 (x,y,z) 值后，可以求解对应的 t 值")
print("3. 约束条件: |MF|² ≥ 100，即 MF_squared ≥ 100")
print("4. 这个方程描述了一个3D空间中的圆锥曲线")

# 输出系数形式（如果需要的话）
print("\n=== 系数分析 ===")
print("MF_squared =", expand(MF_squared))
print("MC_squared =", expand(MC_squared))
print("dot_MF_MC =", expand(dot_MF_MC))