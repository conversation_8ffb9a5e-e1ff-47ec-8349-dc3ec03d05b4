import sympy as sp
from sympy import symbols, sqrt

# 定义符号变量
t, x, y, z = symbols('t x y z', real=True)

def get_original_conic_equation():
    """
    返回最初的3D圆锥曲线方程
    sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)
    """
    
    # 定义3D向量
    MC = [x - 20000 + 298.5185*t, y, z - 2000 + 29.776*t]
    MF = [298.5185*t - 2812, 0, 26.776*t - 248.204]
    
    # 计算向量模长
    abs_MC = sqrt(MC[0]**2 + MC[1]**2 + MC[2]**2)
    abs_MF = sqrt(MF[0]**2 + MF[1]**2 + MF[2]**2)
    
    # 计算点积
    dot_MF_MC = MF[0]*MC[0] + MF[1]*MC[1] + MF[2]*MC[2]
    
    # 原始等式的左边和右边
    left_side = sqrt(1 - 100/abs_MF)
    right_side = dot_MF_MC / (abs_MF * abs_MC)
    
    # 建立等式 F(x,y,z,t) = 0
    equation = left_side - right_side
    
    return equation, MC, MF, abs_MC, abs_MF, dot_MF_MC

if __name__ == "__main__":
    print("=== 最初的3D圆锥曲线方程 ===\n")
    
    equation, MC, MF, abs_MC, abs_MF, dot_MF_MC = get_original_conic_equation()
    
    print("向量定义:")
    print("MC =", MC)
    print("MF =", MF)
    print()
    
    print("模长:")
    print("|MC| =", abs_MC)
    print("|MF| =", abs_MF)
    print()
    
    print("点积:")
    print("MF·MC =", dot_MF_MC)
    print()
    
    print("=== 原始等式 ===")
    print("sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)")
    print()
    
    print("等价形式:")
    print("F(x,y,z,t) = sqrt(1 - 100/|MF|) - (MF·MC)/(|MF|*|MC|) = 0")
    print()
    
    print("完整表达式:")
    print("F(x,y,z,t) =", equation)
    print()
    
    print("=== 使用方法 ===")
    print("1. 代入具体的 (x,y,z) 坐标")
    print("2. 求解方程 F(x,y,z,t) = 0 得到 t 值")
    print("3. 约束条件: |MF| ≥ 10")
    print("4. 保持了最初等式的优美形式，便于理解几何意义")
    
    return equation
