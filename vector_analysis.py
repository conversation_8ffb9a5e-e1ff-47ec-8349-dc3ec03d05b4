import sympy as sp
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# 定义符号变量
t, x, y, z = sp.symbols('t x y z', real=True)

def analyze_3d_vectors():
    """分析3D向量MC和MF的关系"""
    
    print("=== 3D向量分析 ===")
    print()
    
    # 定义向量
    MC = sp.Matrix([x - 20000 + 298.5185*t, y, z - 2000 + 29.776*t])
    MF = sp.Matrix([298.5185*t - 2812, 0, 26.776*t - 248.204])
    
    print("向量MC =", MC.T)
    print("向量MF =", MF.T)
    print()
    
    # 计算模长
    abs_MC = sp.sqrt(MC.dot(MC))
    abs_MF = sp.sqrt(MF.dot(MF))
    
    print("MC的模长 |MC| =", abs_MC)
    print("MF的模长 |MF| =", abs_MF)
    print()
    
    # 计算点积
    dot_MF_MC = MF.dot(MC)
    print("点积 MF·MC =", dot_MF_MC)
    print()
    
    # 原始等式分析
    print("=== 等式分析 ===")
    print("原始等式: sqrt(1 - 100/|MF|) = (MF·MC)/(|MF|*|MC|)")
    print()
    
    # 等式的几何意义
    print("几何意义分析:")
    print("- 右边 (MF·MC)/(|MF|*|MC|) 是两向量夹角的余弦值: cos(θ)")
    print("- 左边 sqrt(1 - 100/|MF|) 涉及到|MF|的约束")
    print("- 这个等式描述了一个圆锥曲线")
    print()
    
    # 建立等式
    left_side = sp.sqrt(1 - 100/abs_MF)
    right_side = dot_MF_MC / (abs_MF * abs_MC)
    
    equation = sp.Eq(left_side, right_side)
    print("完整等式:", equation)
    print()
    
    return MC, MF, abs_MC, abs_MF, dot_MF_MC, equation

def solve_specific_case():
    """求解特定情况"""
    print("=== 特定情况求解 ===")
    
    # 假设一个具体的点 (x, y, z)
    x_val, y_val, z_val = 0, 0, 0  # 可以修改这些值
    
    print(f"假设点坐标: ({x_val}, {y_val}, {z_val})")
    
    # 代入具体值
    MC_specific = sp.Matrix([x_val - 20000 + 298.5185*t, y_val, z_val - 2000 + 29.776*t])
    MF_specific = sp.Matrix([298.5185*t - 2812, 0, 26.776*t - 248.204])
    
    abs_MC_specific = sp.sqrt(MC_specific.dot(MC_specific))
    abs_MF_specific = sp.sqrt(MF_specific.dot(MF_specific))
    dot_specific = MF_specific.dot(MC_specific)
    
    # 建立方程
    left = sp.sqrt(1 - 100/abs_MF_specific)
    right = dot_specific / (abs_MF_specific * abs_MC_specific)
    
    equation_specific = sp.Eq(left, right)
    
    print("代入后的等式:", equation_specific)
    print()
    
    # 尝试求解t
    try:
        solutions = sp.solve(equation_specific, t)
        print("t的解:")
        for i, sol in enumerate(solutions):
            print(f"  t_{i+1} = {sol}")
    except Exception as e:
        print("无法直接求解，方程过于复杂")
        print("错误信息:", str(e))
    
    return equation_specific

def analyze_constraint():
    """分析约束条件"""
    print("=== 约束条件分析 ===")
    
    # 分析 sqrt(1 - 100/|MF|) 的定义域
    print("约束条件: sqrt(1 - 100/|MF|) 要有意义")
    print("需要: 1 - 100/|MF| ≥ 0")
    print("即: |MF| ≥ 100")
    print()
    
    # MF的模长表达式
    MF_magnitude_squared = (298.5185*t - 2812)**2 + (26.776*t - 248.204)**2
    print("MF模长的平方:", MF_magnitude_squared)
    
    # 展开并简化
    MF_mag_sq_expanded = sp.expand(MF_magnitude_squared)
    print("展开后:", MF_mag_sq_expanded)
    
    # 求解 |MF| = 100 的边界条件
    boundary_eq = sp.Eq(MF_magnitude_squared, 10000)  # 100^2 = 10000
    print("边界条件 |MF| = 100:")
    print(boundary_eq)
    
    try:
        boundary_solutions = sp.solve(boundary_eq, t)
        print("边界t值:")
        for sol in boundary_solutions:
            print(f"  t = {sol} ≈ {float(sol.evalf())}")
    except:
        print("无法求解边界条件")
    
    return MF_magnitude_squared

def create_parametric_analysis():
    """创建参数化分析"""
    print("=== 参数化分析 ===")
    
    # 将等式重新整理
    print("重新整理等式以便分析:")
    print("设 cos(θ) = (MF·MC)/(|MF|*|MC|)")
    print("则原等式变为: sqrt(1 - 100/|MF|) = cos(θ)")
    print()
    
    print("这意味着:")
    print("1. |MF| ≥ 100 (约束条件)")
    print("2. cos(θ) = sqrt(1 - 100/|MF|)")
    print("3. 当|MF|增大时，cos(θ)趋近于1，即θ趋近于0")
    print("4. 当|MF| = 100时，cos(θ) = 0，即θ = 90°")
    print()
    
    return True

if __name__ == "__main__":
    # 执行分析
    MC, MF, abs_MC, abs_MF, dot_MF_MC, equation = analyze_3d_vectors()
    print("\n" + "="*50 + "\n")
    
    equation_specific = solve_specific_case()
    print("\n" + "="*50 + "\n")
    
    MF_mag_sq = analyze_constraint()
    print("\n" + "="*50 + "\n")
    
    create_parametric_analysis()
    
    print("\n=== 总结 ===")
    print("你的等式描述了一个复杂的3D圆锥曲线，其中:")
    print("1. MC和MF是关于参数t的3D向量")
    print("2. |MC|和|MF|分别是这两个向量的模长")
    print("3. 等式建立了向量夹角与模长之间的关系")
    print("4. 这形成了一个参数化的圆锥曲线")
    print("\n要求解具体的t值，需要给定x, y, z的具体数值。")
